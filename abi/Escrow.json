[{"type": "constructor", "inputs": [{"name": "_token", "type": "address", "internalType": "address"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "agreementCount", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "approveMilestone", "inputs": [{"name": "agreementID", "type": "uint256", "internalType": "uint256"}, {"name": "milestoneIndex", "type": "uint16", "internalType": "uint16"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "createAgreement", "inputs": [{"name": "approvers", "type": "address[]", "internalType": "address[]"}, {"name": "payoutRecipient", "type": "address", "internalType": "address"}, {"name": "numApprovalsRequired", "type": "uint8", "internalType": "uint8"}, {"name": "milestones", "type": "tuple[]", "internalType": "struct IEscrow.Milestone[]", "components": [{"name": "milestonePaymentAmount", "type": "uint256", "internalType": "uint256"}, {"name": "numApprovals", "type": "uint256", "internalType": "uint256"}]}], "outputs": [{"name": "agreementID", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "getAgreement", "inputs": [{"name": "agreementID", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "tuple", "internalType": "struct IEscrow.Agreement", "components": [{"name": "approvers", "type": "address[]", "internalType": "address[]"}, {"name": "payoutRecipient", "type": "address", "internalType": "address"}, {"name": "creator", "type": "address", "internalType": "address"}, {"name": "numApprovalsRequired", "type": "uint8", "internalType": "uint8"}, {"name": "milestones", "type": "tuple[]", "internalType": "struct IEscrow.Milestone[]", "components": [{"name": "milestonePaymentAmount", "type": "uint256", "internalType": "uint256"}, {"name": "numApprovals", "type": "uint256", "internalType": "uint256"}]}]}], "stateMutability": "view"}, {"type": "function", "name": "getMilestoneApprovals", "inputs": [{"name": "agreementID", "type": "uint256", "internalType": "uint256"}, {"name": "milestoneIndex", "type": "uint16", "internalType": "uint16"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "token", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract IERC20"}], "stateMutability": "view"}, {"type": "event", "name": "AgreementCreated", "inputs": [{"name": "agreementID", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "error", "name": "AlreadyApproved", "inputs": []}, {"type": "error", "name": "NotApprover", "inputs": []}]