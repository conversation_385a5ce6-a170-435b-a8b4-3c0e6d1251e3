// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.13;

import {IERC20} from "openzeppelin-contracts/contracts/interfaces/IERC20.sol";

import {IEscrow} from "./IEscrow.sol";

contract Escrow is IEscrow {
    uint256 public agreementCount;
    IERC20 public token;

    mapping(uint256 => address) private _payoutRecipients;
    mapping(uint256 => uint8) private _numApprovalsRequired;
    mapping(uint256 => mapping(uint16 => mapping(address => bool)))
        private _hasApproved;
    mapping(uint256 => address[]) private _approvers;
    mapping(uint256 => address) private _creators;
    mapping(uint256 => Milestone[]) private _milestones;

    constructor(address _token) {
        token = IERC20(_token);
    }

    error NotApprover();
    error AlreadyApproved();

    event AgreementCreated(uint256 agreementID);

    modifier onlyApprover(uint256 agreementID) {
        bool isApprover = false;
        address[] memory approvers = _approvers[agreementID];
        for (uint256 i = 0; i < approvers.length; i++) {
            if (approvers[i] == msg.sender) {
                isApprover = true;
                break;
            }
        }
        if (!isApprover) {
            revert NotApprover();
        }
        _;
    }

    function createAgreement(
        address[] memory approvers,
        address payoutRecipient,
        uint8 numApprovalsRequired,
        Milestone[] memory milestones
    ) external returns (uint256 agreementID) {
        agreementCount += 1;

        _payoutRecipients[agreementCount] = payoutRecipient;
        _approvers[agreementCount] = approvers;
        _numApprovalsRequired[agreementCount] = numApprovalsRequired;
        _creators[agreementCount] = msg.sender;

        uint256 totalMilestonePaymentAmount = 0;

        for (uint256 i = 0; i < milestones.length; i++) {
            totalMilestonePaymentAmount += milestones[i].milestonePaymentAmount;

            _milestones[agreementCount].push(milestones[i]);
        }

        token.transferFrom(
            msg.sender,
            address(this),
            totalMilestonePaymentAmount
        );

        emit AgreementCreated(agreementCount);

        return agreementCount;
    }

    function approveMilestone(
        uint256 agreementID,
        uint16 milestoneIndex
    ) external onlyApprover(agreementID) {
        bool hasApproved = _hasApproved[agreementID][milestoneIndex][
            msg.sender
        ];
        if (hasApproved) {
            revert AlreadyApproved();
        }

        _hasApproved[agreementID][milestoneIndex][msg.sender] = true;
        _milestones[agreementID][milestoneIndex].numApprovals += 1;

        if (
            _milestones[agreementID][milestoneIndex].numApprovals ==
            _numApprovalsRequired[agreementID]
        ) {
            token.transfer(
                _payoutRecipients[agreementID],
                _milestones[agreementID][milestoneIndex].milestonePaymentAmount
            );
        }
    }

    function getAgreement(
        uint256 agreementID
    ) external view returns (Agreement memory) {
        address[] memory approvers = _approvers[agreementID];
        Milestone[] memory milestones = _milestones[agreementID];
        address creator = _creators[agreementID];
        uint8 numApprovalsRequired = _numApprovalsRequired[agreementID];
        address payoutRecipient = _payoutRecipients[agreementID];

        return
            Agreement({
                approvers: approvers,
                payoutRecipient: payoutRecipient,
                creator: creator,
                numApprovalsRequired: numApprovalsRequired,
                milestones: milestones
            });
    }

    function getMilestoneApprovals(
        uint256 agreementID,
        uint16 milestoneIndex
    ) external view returns (uint256) {
        return _milestones[agreementID][milestoneIndex].numApprovals;
    }
}
