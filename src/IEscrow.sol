// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.13;

interface IEscrow {
    struct Milestone {
        uint256 milestonePaymentAmount;
        uint256 numApprovals;
    }

    struct Agreement {
        // addresses who are capable of granting an approval for a milestone
        address[] approvers;
        address payoutRecipient;
        address creator;
        // number of unique approvals required to consider a milestone complete
        uint8 numApprovalsRequired;
        Milestone[] milestones;
    }

    // createAgreement creates an escrow agreement and pulls the total funds
    // across the agremeent milestones from the caller to be held in escrow
    // by the contract.
    function createAgreement(
        address[] memory approvers,
        address payoutRecipient,
        uint8 numApprovalsRequired,
        Milestone[] memory milestones
    ) external returns (uint256 agreementID);

    // Approves an agreement milestone. The milestone is identified by the id of the agreement
    // and the index of the milestone in the agreement's milestone list. The caller must be
    // in the list of agreement approvers.
    // If the milestone receives the required number of approvals the milestone payment is
    // sent to the payoutRecipient of the escrow agreement.
    function approveMilestone(
        uint256 agreementId,
        uint16 milestoneIndex
    ) external;
}
