// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.13;

import {Test} from "forge-std/Test.sol";
import {console} from "forge-std/console.sol";

import {ERC20Mock} from "openzeppelin-contracts/contracts/mocks/token/ERC20Mock.sol";
import {IERC20} from "openzeppelin-contracts/contracts/interfaces/IERC20.sol";

import {Escrow, IEscrow} from "../src/Escrow.sol";

contract EscrowTest is Test {
    IERC20 token;
    Escrow escrow;

    address alice = makeAddr("alice");
    address bob = makeAddr("bob");
    address charlie = makeAddr("charlie");
    address david = makeAddr("david");

    function setUp() public {
        token = new ERC20Mock();
        escrow = new Escrow(address(token));
    }

    function test_createAgreement() public {
        deal(address(token), address(this), 10 ether);

        token.approve(address(escrow), 6 ether);

        address[] memory approvers = new address[](3);
        approvers[0] = alice;
        approvers[1] = bob;
        approvers[2] = charlie;

        IEscrow.Milestone[] memory milestones = new IEscrow.Milestone[](3);
        milestones[0] = IEscrow.Milestone({
            milestonePaymentAmount: 1 ether,
            numApprovals: 0
        });
        milestones[1] = IEscrow.Milestone({
            milestonePaymentAmount: 2 ether,
            numApprovals: 0
        });
        milestones[2] = IEscrow.Milestone({
            milestonePaymentAmount: 3 ether,
            numApprovals: 0
        });

        uint256 contractBalanceBefore = token.balanceOf(address(escrow));
        uint256 ownerBalanceBefore = token.balanceOf(address(this));

        uint256 agreementID = escrow.createAgreement(
            approvers,
            address(this),
            2,
            milestones
        );

        uint256 contractBalanceAfter = token.balanceOf(address(escrow));
        uint256 ownerBalanceAfter = token.balanceOf(address(this));

        assertEq(contractBalanceAfter, contractBalanceBefore + 6 ether);
        assertEq(ownerBalanceAfter, ownerBalanceBefore - 6 ether);

        assertEq(agreementID, 1);

        IEscrow.Agreement memory retrievedAgreement = escrow.getAgreement(
            agreementID
        );

        assertEq(retrievedAgreement.payoutRecipient, address(this));
        assertEq(retrievedAgreement.creator, address(this));
        assertEq(retrievedAgreement.numApprovalsRequired, 2);

        assertEq(retrievedAgreement.approvers.length, 3);
        for (uint256 i = 0; i < retrievedAgreement.approvers.length; i++) {
            assertEq(retrievedAgreement.approvers[i], approvers[i]);
        }

        assertEq(retrievedAgreement.milestones.length, 3);
        for (uint256 i = 0; i < retrievedAgreement.milestones.length; i++) {
            assertEq(
                retrievedAgreement.milestones[i].milestonePaymentAmount,
                milestones[i].milestonePaymentAmount
            );
            assertEq(
                retrievedAgreement.milestones[i].numApprovals,
                milestones[i].numApprovals
            );
        }
    }

    function test_approveMilestone() public {
        deal(address(token), address(this), 10 ether);

        token.approve(address(escrow), 6 ether);

        address[] memory approvers = new address[](3);
        approvers[0] = alice;
        approvers[1] = bob;
        approvers[2] = charlie;

        IEscrow.Milestone[] memory milestones = new IEscrow.Milestone[](3);
        milestones[0] = IEscrow.Milestone({
            milestonePaymentAmount: 1 ether,
            numApprovals: 0
        });
        milestones[1] = IEscrow.Milestone({
            milestonePaymentAmount: 2 ether,
            numApprovals: 0
        });
        milestones[2] = IEscrow.Milestone({
            milestonePaymentAmount: 3 ether,
            numApprovals: 0
        });

        uint256 agreementID = escrow.createAgreement(
            approvers,
            address(this),
            2,
            milestones
        );

        uint256 approvalsBefore = escrow.getMilestoneApprovals(agreementID, 0);
        assertEq(approvalsBefore, 0);

        vm.prank(alice);
        escrow.approveMilestone(agreementID, 0);

        uint256 approvalsAfter = escrow.getMilestoneApprovals(agreementID, 0);
        assertEq(approvalsAfter, 1);
    }

    function test_approveMilestone_PaysOutWhenApprovalsAreMet() public {
        deal(address(token), address(this), 10 ether);

        token.approve(address(escrow), 6 ether);

        address[] memory approvers = new address[](3);
        approvers[0] = alice;
        approvers[1] = bob;
        approvers[2] = charlie;

        IEscrow.Milestone[] memory milestones = new IEscrow.Milestone[](3);
        milestones[0] = IEscrow.Milestone({
            milestonePaymentAmount: 1 ether,
            numApprovals: 0
        });
        milestones[1] = IEscrow.Milestone({
            milestonePaymentAmount: 2 ether,
            numApprovals: 0
        });
        milestones[2] = IEscrow.Milestone({
            milestonePaymentAmount: 3 ether,
            numApprovals: 0
        });

        uint256 agreementID = escrow.createAgreement(
            approvers,
            address(this),
            2,
            milestones
        );

        uint256 balanceBefore = token.balanceOf(address(this));

        vm.prank(alice);
        escrow.approveMilestone(agreementID, 0);

        vm.prank(bob);
        escrow.approveMilestone(agreementID, 0);

        uint256 balanceAfter = token.balanceOf(address(this));
        assertEq(balanceAfter, balanceBefore + 1 ether);
    }

    function test_approveMilestone_DoesNotPayOutMilestoneTwice() public {
        deal(address(token), address(this), 10 ether);

        token.approve(address(escrow), 6 ether);

        address[] memory approvers = new address[](3);
        approvers[0] = alice;
        approvers[1] = bob;
        approvers[2] = charlie;

        IEscrow.Milestone[] memory milestones = new IEscrow.Milestone[](3);
        milestones[0] = IEscrow.Milestone({
            milestonePaymentAmount: 1 ether,
            numApprovals: 0
        });
        milestones[1] = IEscrow.Milestone({
            milestonePaymentAmount: 2 ether,
            numApprovals: 0
        });
        milestones[2] = IEscrow.Milestone({
            milestonePaymentAmount: 3 ether,
            numApprovals: 0
        });

        uint256 agreementID = escrow.createAgreement(
            approvers,
            address(this),
            2,
            milestones
        );

        uint256 balanceBefore = token.balanceOf(address(this));

        vm.prank(alice);
        escrow.approveMilestone(agreementID, 0);

        vm.prank(bob);
        escrow.approveMilestone(agreementID, 0);

        uint256 balanceAfter = token.balanceOf(address(this));
        assertEq(balanceAfter, balanceBefore + 1 ether);

        vm.prank(charlie);
        escrow.approveMilestone(agreementID, 0);

        uint256 balanceAfter2 = token.balanceOf(address(this));
        assertEq(balanceAfter2, balanceAfter);
    }

    function test_approveMilestone_RevertsWhenNotApprover() public {
        deal(address(token), address(this), 10 ether);

        token.approve(address(escrow), 6 ether);

        address[] memory approvers = new address[](3);
        approvers[0] = alice;
        approvers[1] = bob;
        approvers[2] = charlie;

        IEscrow.Milestone[] memory milestones = new IEscrow.Milestone[](3);
        milestones[0] = IEscrow.Milestone({
            milestonePaymentAmount: 1 ether,
            numApprovals: 0
        });
        milestones[1] = IEscrow.Milestone({
            milestonePaymentAmount: 2 ether,
            numApprovals: 0
        });
        milestones[2] = IEscrow.Milestone({
            milestonePaymentAmount: 3 ether,
            numApprovals: 0
        });

        uint256 agreementID = escrow.createAgreement(
            approvers,
            address(this),
            2,
            milestones
        );

        uint256 approvalsBefore = escrow.getMilestoneApprovals(agreementID, 0);
        assertEq(approvalsBefore, 0);

        vm.prank(david);
        vm.expectRevert(Escrow.NotApprover.selector);
        escrow.approveMilestone(agreementID, 0);

        uint256 approvalsAfter = escrow.getMilestoneApprovals(agreementID, 0);
        assertEq(approvalsAfter, 0);
    }

    function test_approveMilestone_RevertsWhenApprovedTwice() public {
        deal(address(token), address(this), 10 ether);

        token.approve(address(escrow), 6 ether);

        address[] memory approvers = new address[](3);
        approvers[0] = alice;
        approvers[1] = bob;
        approvers[2] = charlie;

        IEscrow.Milestone[] memory milestones = new IEscrow.Milestone[](3);
        milestones[0] = IEscrow.Milestone({
            milestonePaymentAmount: 1 ether,
            numApprovals: 0
        });
        milestones[1] = IEscrow.Milestone({
            milestonePaymentAmount: 2 ether,
            numApprovals: 0
        });
        milestones[2] = IEscrow.Milestone({
            milestonePaymentAmount: 3 ether,
            numApprovals: 0
        });

        uint256 agreementID = escrow.createAgreement(
            approvers,
            address(this),
            2,
            milestones
        );

        uint256 approvalsBefore = escrow.getMilestoneApprovals(agreementID, 0);
        assertEq(approvalsBefore, 0);

        vm.prank(alice);
        escrow.approveMilestone(agreementID, 0);

        vm.prank(alice);
        vm.expectRevert(Escrow.AlreadyApproved.selector);
        escrow.approveMilestone(agreementID, 0);

        uint256 approvalsAfter = escrow.getMilestoneApprovals(agreementID, 0);
        assertEq(approvalsAfter, 1);
    }
}
